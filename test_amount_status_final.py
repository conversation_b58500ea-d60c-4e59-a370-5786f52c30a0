#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار شامل لتنسيق ألوان حالة المبلغ - الحل النهائي
"""

import sys
from PySide6.QtWidgets import QApplication, QMainWindow, QTableWidget, QTableWidgetItem, QVBoxLayout, QWidget, QHBoxLayout, QLabel
from PySide6.QtCore import Qt
from PySide6.QtGui import QColor, QBrush, QFont

class TestAmountStatusFinal(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("اختبار شامل - تنسيق ألوان حالة المبلغ")
        self.setGeometry(100, 100, 1200, 800)
        self.setLayoutDirection(Qt.RightToLeft)
        
        # إنشاء الواجهة
        self.setup_ui()
        
        # تحميل البيانات التجريبية
        self.load_test_data()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        
        # عنوان رئيسي
        title = QLabel("اختبار تنسيق ألوان حالة المبلغ")
        title.setStyleSheet("""
            QLabel {
                font-size: 20px;
                font-weight: bold;
                color: #2c3e50;
                padding: 15px;
                background-color: #ecf0f1;
                border-radius: 8px;
                margin-bottom: 20px;
            }
        """)
        title.setAlignment(Qt.AlignCenter)
        layout.addWidget(title)
        
        # تخطيط أفقي للجدولين
        tables_layout = QHBoxLayout()
        
        # الجدول الأول - محاكاة جدول مراحل المشروع
        phases_layout = QVBoxLayout()
        phases_label = QLabel("جدول مراحل المشروع")
        phases_label.setStyleSheet("font-size: 16px; font-weight: bold; color: #9b59b6; padding: 10px;")
        phases_label.setAlignment(Qt.AlignCenter)
        phases_layout.addWidget(phases_label)
        
        self.phases_table = QTableWidget()
        self.phases_table.setColumnCount(10)
        self.phases_table.setHorizontalHeaderLabels([
            "ID", "الرقم", "اسم المرحلة", "وصف المرحلة", "الوحدة", 
            "الكمية", "السعر", "الإجمالي", "ملاحظات", "حالة المبلغ"
        ])
        self.phases_table.hideColumn(0)  # إخفاء عمود ID
        phases_layout.addWidget(self.phases_table)
        
        tables_layout.addLayout(phases_layout)
        
        # الجدول الثاني - محاكاة جدول مهام المهندسين
        engineers_layout = QVBoxLayout()
        engineers_label = QLabel("جدول مهام المهندسين")
        engineers_label.setStyleSheet("font-size: 16px; font-weight: bold; color: #16a085; padding: 10px;")
        engineers_label.setAlignment(Qt.AlignCenter)
        engineers_layout.addWidget(engineers_label)
        
        self.engineers_table = QTableWidget()
        self.engineers_table.setColumnCount(7)
        self.engineers_table.setHorizontalHeaderLabels([
            "ID", "الرقم", "المهندس", "وصف المرحلة", 
            "% النسبة", "مبلغ المهندس", "حالة المبلغ"
        ])
        self.engineers_table.hideColumn(0)  # إخفاء عمود ID
        engineers_layout.addWidget(self.engineers_table)
        
        tables_layout.addLayout(engineers_layout)
        
        layout.addLayout(tables_layout)
        
        # معلومات الاختبار
        info_label = QLabel("""
        <b>معايير الاختبار:</b><br>
        • النص "غير مدرج" يجب أن يظهر باللون الأحمر RGB(231, 76, 60) مع خط عريض<br>
        • النص "تم الإدراج" يجب أن يظهر باللون الأخضر RGB(46, 125, 50) مع خط عريض<br>
        • الألوان تطبق على النص فقط وليس الخلفية<br>
        • يجب أن تعمل في كلا الجدولين بنفس الطريقة
        """)
        info_label.setStyleSheet("""
            QLabel {
                font-size: 12px;
                color: #34495e;
                padding: 10px;
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 5px;
                margin-top: 10px;
            }
        """)
        layout.addWidget(info_label)
    
    def apply_amount_status_color(self, item, status_text):
        """تطبيق تنسيق الألوان لعمود حالة المبلغ - نفس الدالة من الملف الأصلي"""
        try:
            if status_text == "غير مدرج":
                # تطبيق اللون الأحمر للنص
                item.setForeground(QBrush(QColor(231, 76, 60)))
                # تطبيق الخط العريض
                font = QFont()
                font.setBold(True)
                item.setFont(font)
                print(f"✅ تم تطبيق اللون الأحمر على: '{status_text}'")
            elif status_text == "تم الإدراج":
                # تطبيق اللون الأخضر للنص
                item.setForeground(QBrush(QColor(46, 125, 50)))
                # تطبيق الخط العريض
                font = QFont()
                font.setBold(True)
                item.setFont(font)
                print(f"✅ تم تطبيق اللون الأخضر على: '{status_text}'")
            else:
                # إزالة أي تنسيق للحالات الأخرى
                item.setForeground(QBrush())
                font = QFont()
                font.setBold(False)
                item.setFont(font)
                print(f"ℹ️ لا يوجد تنسيق للحالة: '{status_text}'")
        except Exception as e:
            print(f"❌ خطأ في تطبيق تنسيق الألوان: {e}")
    
    def load_test_data(self):
        """تحميل بيانات تجريبية للاختبار"""
        print("🔄 بدء تحميل البيانات التجريبية...")
        
        # بيانات جدول المراحل
        phases_data = [
            (1, "مرحلة التصميم الأولي", "تصميم المخططات الأولية", "مخطط", 5, 1000, 5000, "تصميم أولي", "غير مدرج"),
            (2, "مرحلة التصميم التفصيلي", "تصميم المخططات التفصيلية", "مخطط", 8, 1500, 12000, "تصميم تفصيلي", "تم الإدراج"),
            (3, "مرحلة المراجعة", "مراجعة وتدقيق المخططات", "مراجعة", 3, 800, 2400, "مراجعة شاملة", "غير مدرج"),
            (4, "مرحلة التسليم النهائي", "تسليم المشروع كاملاً", "مشروع", 1, 15000, 15000, "تسليم نهائي", "تم الإدراج"),
            (5, "مرحلة المتابعة", "متابعة ما بعد التسليم", "خدمة", 2, 500, 1000, "متابعة", "غير مدرج"),
        ]
        
        self.phases_table.setRowCount(len(phases_data))
        
        for row_idx, row_data in enumerate(phases_data):
            for col_idx, data in enumerate(row_data):
                if col_idx == 0:  # عمود ID (مخفي)
                    item = QTableWidgetItem(str(data))
                    self.phases_table.setItem(row_idx, 0, item)
                else:  # باقي الأعمدة مع إزاحة بسبب عمود الرقم التلقائي
                    item = QTableWidgetItem(str(data))
                    
                    # محاذاة الأرقام في المنتصف
                    if col_idx in [4, 5, 6]:  # أعمدة الأرقام
                        item.setTextAlignment(Qt.AlignCenter)
                    
                    # تطبيق تنسيق الألوان لعمود حالة المبلغ
                    if col_idx == 8:  # عمود حالة المبلغ (الفهرس الأصلي 8)
                        self.apply_amount_status_color(item, str(data))
                    
                    self.phases_table.setItem(row_idx, col_idx + 1, item)
        
        # إضافة أرقام تلقائية لجدول المراحل
        for row in range(self.phases_table.rowCount()):
            auto_number_item = QTableWidgetItem(str(row + 1))
            auto_number_item.setTextAlignment(Qt.AlignCenter)
            self.phases_table.setItem(row, 1, auto_number_item)
        
        # بيانات جدول مهام المهندسين
        engineers_data = [
            (1, "أحمد محمد", "مرحلة التصميم الأولي - تصميم المخططات الأولية", 25, 1250, "غير مدرج"),
            (2, "فاطمة علي", "مرحلة التصميم التفصيلي - تصميم المخططات التفصيلية", 30, 3600, "تم الإدراج"),
            (3, "محمد حسن", "مرحلة المراجعة - مراجعة وتدقيق المخططات", 20, 480, "غير مدرج"),
            (4, "سارة أحمد", "مرحلة التسليم النهائي - تسليم المشروع كاملاً", 35, 5250, "تم الإدراج"),
            (5, "علي محمود", "مرحلة المتابعة - متابعة ما بعد التسليم", 15, 150, "غير مدرج"),
        ]
        
        self.engineers_table.setRowCount(len(engineers_data))
        
        for row_idx, row_data in enumerate(engineers_data):
            for col_idx, data in enumerate(row_data):
                if col_idx == 0:  # عمود ID (مخفي)
                    item = QTableWidgetItem(str(data))
                    self.engineers_table.setItem(row_idx, 0, item)
                else:  # باقي الأعمدة مع إزاحة بسبب عمود الرقم التلقائي
                    item = QTableWidgetItem(str(data))
                    
                    # محاذاة الأرقام في المنتصف
                    if col_idx in [3, 4]:  # أعمدة الأرقام
                        item.setTextAlignment(Qt.AlignCenter)
                    
                    # تطبيق تنسيق الألوان لعمود حالة المبلغ
                    if col_idx == 5:  # عمود حالة مبلغ المهندس (الفهرس الأصلي 5)
                        self.apply_amount_status_color(item, str(data))
                    
                    self.engineers_table.setItem(row_idx, col_idx + 1, item)
        
        # إضافة أرقام تلقائية لجدول المهندسين
        for row in range(self.engineers_table.rowCount()):
            auto_number_item = QTableWidgetItem(str(row + 1))
            auto_number_item.setTextAlignment(Qt.AlignCenter)
            self.engineers_table.setItem(row, 1, auto_number_item)
        
        # تعديل عرض الأعمدة
        self.phases_table.resizeColumnsToContents()
        self.engineers_table.resizeColumnsToContents()
        
        print("✅ تم تحميل البيانات التجريبية بنجاح")
        print("🎨 تحقق من ألوان عمود 'حالة المبلغ' في كلا الجدولين")

def main():
    app = QApplication(sys.argv)
    
    # تطبيق الخط العربي
    app.setLayoutDirection(Qt.RightToLeft)
    
    window = TestAmountStatusFinal()
    window.show()
    
    print("🚀 تم فتح نافذة الاختبار")
    print("📋 تحقق من النتائج التالية:")
    print("   • 'غير مدرج' باللون الأحمر الداكن + خط عريض")
    print("   • 'تم الإدراج' باللون الأخضر الداكن + خط عريض")
    print("   • يجب أن تعمل في كلا الجدولين")
    
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
