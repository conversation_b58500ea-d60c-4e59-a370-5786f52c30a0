#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار الاتصال بقاعدة البيانات لحل مشكلة MySQL definer error
"""

import mysql.connector
from متغيرات import host, user, password

def test_database_connection():
    """اختبار الاتصال بقاعدة البيانات"""
    print("🧪 اختبار الاتصال بقاعدة البيانات...")
    print(f"Host: {host}")
    print(f"User: {user}")
    print(f"Password: {'*' * len(str(password))}")
    
    try:
        # محاولة الاتصال بقاعدة البيانات
        db_name = "project_manager_V2"
        conn = mysql.connector.connect(
            host=host,
            user=user,
            password=password,
            database=db_name
        )
        
        cursor = conn.cursor()
        
        # اختبار استعلام بسيط
        cursor.execute("SELECT 1 as test")
        result = cursor.fetchone()
        print(f"✅ تم الاتصال بقاعدة البيانات بنجاح: {result}")
        
        # اختبار وجود الجداول المطلوبة
        cursor.execute("SHOW TABLES LIKE 'المشاريع_المدفوعات'")
        table_exists = cursor.fetchone()
        if table_exists:
            print("✅ جدول المشاريع_المدفوعات موجود")
        else:
            print("❌ جدول المشاريع_المدفوعات غير موجود")
        
        # اختبار استعلام على الجدول
        cursor.execute("SELECT COUNT(*) FROM المشاريع_المدفوعات")
        count = cursor.fetchone()[0]
        print(f"✅ عدد السجلات في جدول المشاريع_المدفوعات: {count}")
        
        # اختبار وجود جدول المشاريع
        cursor.execute("SHOW TABLES LIKE 'المشاريع'")
        projects_table_exists = cursor.fetchone()
        if projects_table_exists:
            print("✅ جدول المشاريع موجود")
        else:
            print("❌ جدول المشاريع غير موجود")
        
        conn.close()
        print("✅ تم إغلاق الاتصال بنجاح")
        return True
        
    except mysql.connector.Error as err:
        print(f"❌ خطأ في الاتصال بقاعدة البيانات: {err}")
        print(f"Error Code: {err.errno}")
        print(f"SQL State: {err.sqlstate}")
        return False
    except Exception as e:
        print(f"❌ خطأ عام: {e}")
        return False

def test_user_permissions():
    """اختبار صلاحيات المستخدم"""
    print("\n🔐 اختبار صلاحيات المستخدم...")
    
    try:
        # الاتصال بدون تحديد قاعدة بيانات
        conn = mysql.connector.connect(
            host=host,
            user=user,
            password=password
        )
        
        cursor = conn.cursor()
        
        # عرض المستخدم الحالي
        cursor.execute("SELECT USER(), CURRENT_USER()")
        user_info = cursor.fetchone()
        print(f"المستخدم الحالي: {user_info[0]}")
        print(f"المستخدم المعرف: {user_info[1]}")
        
        # عرض الصلاحيات
        cursor.execute("SHOW GRANTS")
        grants = cursor.fetchall()
        print("الصلاحيات:")
        for grant in grants:
            print(f"  - {grant[0]}")
        
        conn.close()
        return True
        
    except mysql.connector.Error as err:
        print(f"❌ خطأ في اختبار الصلاحيات: {err}")
        return False

if __name__ == "__main__":
    print("=" * 50)
    print("اختبار حل مشكلة MySQL Definer Error")
    print("=" * 50)
    
    # اختبار الاتصال
    connection_success = test_database_connection()
    
    # اختبار الصلاحيات
    permissions_success = test_user_permissions()
    
    print("\n" + "=" * 50)
    if connection_success and permissions_success:
        print("✅ جميع الاختبارات نجحت - يجب أن تعمل دفعات المشروع الآن")
    else:
        print("❌ بعض الاختبارات فشلت - قد تحتاج إلى إعدادات إضافية")
    print("=" * 50)
