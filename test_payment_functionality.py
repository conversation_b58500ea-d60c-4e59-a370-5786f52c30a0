#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار وظائف دفعات المشروع للتأكد من حل مشكلة MySQL definer error
"""

import mysql.connector
from متغيرات import host, user, password
from datetime import datetime

def test_payment_operations():
    """اختبار العمليات الأساسية لدفعات المشروع"""
    print("🧪 اختبار عمليات دفعات المشروع...")
    
    try:
        # الاتصال بقاعدة البيانات
        db_name = "project_manager_V2"
        conn = mysql.connector.connect(
            host=host, 
            user=user, 
            password=password,
            database=db_name
        )
        cursor = conn.cursor()
        
        print("✅ تم الاتصال بقاعدة البيانات بنجاح")
        
        # اختبار 1: التحقق من وجود الجداول المطلوبة
        cursor.execute("SHOW TABLES LIKE 'المشاريع_المدفوعات'")
        if cursor.fetchone():
            print("✅ جدول المشاريع_المدفوعات موجود")
        else:
            print("❌ جدول المشاريع_المدفوعات غير موجود")
            return False
            
        cursor.execute("SHOW TABLES LIKE 'المشاريع'")
        if cursor.fetchone():
            print("✅ جدول المشاريع موجود")
        else:
            print("❌ جدول المشاريع غير موجود")
            return False
        
        # اختبار 2: اختبار استعلام SELECT على جدول المدفوعات
        cursor.execute("SELECT COUNT(*) FROM المشاريع_المدفوعات")
        count = cursor.fetchone()[0]
        print(f"✅ عدد السجلات في جدول المدفوعات: {count}")
        
        # اختبار 3: اختبار استعلام SELECT على جدول المشاريع
        cursor.execute("SELECT COUNT(*) FROM المشاريع")
        projects_count = cursor.fetchone()[0]
        print(f"✅ عدد المشاريع في قاعدة البيانات: {projects_count}")
        
        # اختبار 4: اختبار استعلام معقد مع JOIN (مثل الذي يستخدم في التقارير)
        cursor.execute("""
            SELECT COUNT(*) 
            FROM المشاريع_المدفوعات dp
            LEFT JOIN المشاريع p ON dp.معرف_المشروع = p.id
        """)
        join_count = cursor.fetchone()[0]
        print(f"✅ اختبار JOIN بين الجدولين نجح: {join_count} سجل")
        
        # اختبار 5: اختبار استعلام DESCRIBE (مثل الذي يستخدم في تحديث الواجهة)
        cursor.execute("DESCRIBE المشاريع_المدفوعات")
        columns = cursor.fetchall()
        print(f"✅ اختبار DESCRIBE نجح: {len(columns)} عمود في جدول المدفوعات")
        
        cursor.execute("DESCRIBE المشاريع")
        project_columns = cursor.fetchall()
        print(f"✅ اختبار DESCRIBE نجح: {len(project_columns)} عمود في جدول المشاريع")
        
        # اختبار 6: اختبار إدراج وحذف تجريبي (إذا كان هناك مشاريع)
        if projects_count > 0:
            # جلب أول مشروع للاختبار
            cursor.execute("SELECT id, معرف_العميل FROM المشاريع LIMIT 1")
            project_data = cursor.fetchone()
            if project_data:
                project_id, client_id = project_data
                
                # إدراج دفعة تجريبية
                test_payment_data = (
                    client_id,
                    project_id,
                    "دفعة اختبار",
                    100.0,
                    datetime.now().strftime("%Y-%m-%d"),
                    "دفع نقدًا",
                    "اختبار النظام"
                )
                
                cursor.execute("""
                    INSERT INTO المشاريع_المدفوعات 
                    (معرف_العميل, معرف_المشروع, وصف_المدفوع, المبلغ_المدفوع, تاريخ_الدفع, طريقة_الدفع, المستلم)
                    VALUES (%s, %s, %s, %s, %s, %s, %s)
                """, test_payment_data)
                
                test_payment_id = cursor.lastrowid
                print(f"✅ تم إدراج دفعة تجريبية بنجاح: ID {test_payment_id}")
                
                # حذف الدفعة التجريبية
                cursor.execute("DELETE FROM المشاريع_المدفوعات WHERE id = %s", (test_payment_id,))
                print("✅ تم حذف الدفعة التجريبية بنجاح")
                
                conn.commit()
        else:
            print("⚠️ لا توجد مشاريع للاختبار عليها")
        
        conn.close()
        print("✅ تم إغلاق الاتصال بنجاح")
        return True
        
    except mysql.connector.Error as err:
        print(f"❌ خطأ في قاعدة البيانات: {err}")
        print(f"Error Code: {err.errno}")
        if hasattr(err, 'sqlstate'):
            print(f"SQL State: {err.sqlstate}")
        return False
    except Exception as e:
        print(f"❌ خطأ عام: {e}")
        return False

def test_specific_queries():
    """اختبار الاستعلامات المحددة التي تسبب مشاكل"""
    print("\n🔍 اختبار الاستعلامات المحددة...")
    
    try:
        db_name = "project_manager_V2"
        conn = mysql.connector.connect(
            host=host, 
            user=user, 
            password=password,
            database=db_name
        )
        cursor = conn.cursor()
        
        # اختبار الاستعلام الذي كان يسبب مشكلة في السطر 602
        cursor.execute("SELECT COUNT(*) FROM المشاريع WHERE id > 0")
        result = cursor.fetchone()
        print(f"✅ اختبار استعلام المشاريع نجح: {result[0]} مشروع")
        
        # اختبار استعلام التحديث
        cursor.execute("SELECT COUNT(*) FROM المشاريع WHERE الحالة IS NOT NULL")
        result = cursor.fetchone()
        print(f"✅ اختبار استعلام الحالة نجح: {result[0]} مشروع له حالة")
        
        conn.close()
        return True
        
    except mysql.connector.Error as err:
        print(f"❌ خطأ في الاستعلامات المحددة: {err}")
        return False

if __name__ == "__main__":
    print("=" * 60)
    print("اختبار وظائف دفعات المشروع بعد حل مشكلة MySQL Definer")
    print("=" * 60)
    
    # اختبار العمليات الأساسية
    basic_test_success = test_payment_operations()
    
    # اختبار الاستعلامات المحددة
    specific_test_success = test_specific_queries()
    
    print("\n" + "=" * 60)
    if basic_test_success and specific_test_success:
        print("✅ جميع الاختبارات نجحت - دفعات المشروع جاهزة للاستخدام!")
        print("✅ تم حل مشكلة MySQL Definer Error بنجاح")
    else:
        print("❌ بعض الاختبارات فشلت - قد تحتاج إلى مراجعة إضافية")
    print("=" * 60)
