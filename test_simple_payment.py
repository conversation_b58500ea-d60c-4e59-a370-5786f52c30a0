#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار بسيط لوظائف دفعات المشروع
"""

import mysql.connector
from متغيرات import host, user, password
from datetime import datetime

def test_simple_payment():
    """اختبار بسيط لإدراج دفعة"""
    print("🧪 اختبار بسيط لدفعات المشروع...")
    
    try:
        # الاتصال بقاعدة البيانات
        db_name = "project_manager_V2"
        conn = mysql.connector.connect(
            host=host, 
            user=user, 
            password=password,
            database=db_name
        )
        cursor = conn.cursor()
        
        print("✅ تم الاتصال بقاعدة البيانات بنجاح")
        
        # التحقق من أعمدة جدول المشاريع_المدفوعات
        cursor.execute("DESCRIBE المشاريع_المدفوعات")
        columns = cursor.fetchall()
        print("\n📋 أعمدة جدول المشاريع_المدفوعات:")
        for col in columns:
            print(f"  - {col[0]} ({col[1]})")
        
        # التحقق من أعمدة جدول المشاريع
        cursor.execute("DESCRIBE المشاريع")
        project_columns = cursor.fetchall()
        print("\n📋 أعمدة جدول المشاريع:")
        for col in project_columns:
            print(f"  - {col[0]} ({col[1]})")
        
        # جلب مشروع للاختبار
        cursor.execute("SELECT id, معرف_العميل FROM المشاريع LIMIT 1")
        project_data = cursor.fetchone()
        
        if project_data:
            project_id, client_id = project_data
            print(f"\n🎯 اختبار على المشروع ID: {project_id}, العميل ID: {client_id}")
            
            # إدراج دفعة تجريبية بالأعمدة الصحيحة فقط
            test_payment_data = (
                client_id,
                project_id,
                100.0,  # المبلغ_المدفوع
                "دفعة اختبار",  # وصف_المدفوع
                datetime.now().strftime("%Y-%m-%d"),  # تاريخ_الدفع
                "دفع نقدًا",  # طريقة_الدفع
                "اختبار النظام"  # المستلم
            )
            
            cursor.execute("""
                INSERT INTO المشاريع_المدفوعات 
                (معرف_العميل, معرف_المشروع, المبلغ_المدفوع, وصف_المدفوع, تاريخ_الدفع, طريقة_الدفع, المستلم)
                VALUES (%s, %s, %s, %s, %s, %s, %s)
            """, test_payment_data)
            
            test_payment_id = cursor.lastrowid
            print(f"✅ تم إدراج دفعة تجريبية بنجاح: ID {test_payment_id}")
            
            # التحقق من الدفعة المدرجة
            cursor.execute("SELECT * FROM المشاريع_المدفوعات WHERE id = %s", (test_payment_id,))
            inserted_payment = cursor.fetchone()
            print(f"✅ تم التحقق من الدفعة: {inserted_payment}")
            
            # حذف الدفعة التجريبية
            cursor.execute("DELETE FROM المشاريع_المدفوعات WHERE id = %s", (test_payment_id,))
            print("✅ تم حذف الدفعة التجريبية بنجاح")
            
            conn.commit()
            
        else:
            print("⚠️ لا توجد مشاريع للاختبار عليها")
        
        conn.close()
        print("✅ تم إغلاق الاتصال بنجاح")
        return True
        
    except mysql.connector.Error as err:
        print(f"❌ خطأ في قاعدة البيانات: {err}")
        print(f"Error Code: {err.errno}")
        if hasattr(err, 'sqlstate'):
            print(f"SQL State: {err.sqlstate}")
        return False
    except Exception as e:
        print(f"❌ خطأ عام: {e}")
        return False

if __name__ == "__main__":
    print("=" * 50)
    print("اختبار بسيط لدفعات المشروع")
    print("=" * 50)
    
    success = test_simple_payment()
    
    print("\n" + "=" * 50)
    if success:
        print("✅ الاختبار نجح - دفعات المشروع تعمل بشكل صحيح!")
    else:
        print("❌ الاختبار فشل")
    print("=" * 50)
